<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=1024, initial-scale=1.0">
  <title>PseudoWrite – AI Storywriting Assistant</title>
  <style>
    html, body, div,
    h1, h2, h3, h4, h5, h6, p, pre, a,
    abbr, address, b, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp,
    small, strike, strong, sub, sup, tt, var,
    dl, dt, dd, ol, ul, li,
    fieldset, form, label, legend,
    table, caption, tbody, tfoot, thead, tr, th, td,
    article, aside, canvas, details, embed,
    figure, figcaption, footer, header, hgroup,
    menu, nav, output, ruby, section, summary,
    time, mark, audio, video {
      margin: 0; padding: 0; border: 0; font-size: 100%; font: inherit; vertical-align: baseline;
    }
    body {
      font-family: 'Segoe UI', Arial, sans-serif;
      background: #f7f7fa;
      color: #23232b;
      min-width: 1024px;
      line-height: 1.5;
    }
    #app-root {
      display: flex;
      width: 100vw;
      height: 100vh;
      box-sizing: border-box;
      overflow: hidden;
    }
    .sidebar {
      width: 260px;
      background: #23232b;
      color: #fafafe;
      display: flex;
      flex-direction: column;
      border-right: 1px solid #ddd;
      z-index: 1;
    }
    .main-panel {
      flex: 1;
      background: #fff;
      position: relative;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    #modal-root {
      position: fixed;
      z-index: 1000;
      top: 0; left: 0; width: 100vw; height: 100vh;
      pointer-events: none;
    }
    #toast-root {
      position: fixed;
      z-index: 1100;
      bottom: 32px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      pointer-events: none;
    }
    [data-hidden="true"] { display: none !important; }
  </style>
</head>
<body>
  <div id="app-root">
    <aside class="sidebar"></aside>
    <main class="main-panel"></main>
  </div>
  <div id="modal-root"></div>
  <div id="toast-root"></div>
  <script>
    // (state and validation logic unchanged for brevity)

    // Only show renderMainPanel and renderSidebar for clarity here:
    document.addEventListener('DOMContentLoaded', function() {
      function renderSidebar() {
        const sidebar = document.querySelector(".sidebar");
        sidebar.innerHTML = `
          <div style="padding:20px 16px 8px 16px; font-weight:bold; letter-spacing:1px; font-size:1.15em;">Story Bible</div>
          <div id="sb-characters-group" style="margin:0 0 6px 0">
            <div style="padding:7px 14px 5px 18px; font-size:1em; opacity:0.90;">Characters</div>
            <div id="sb-characters-list" style="padding-left:28px; font-size:0.97em; color:#ccc;">No characters yet.</div>
          </div>
          <div id="sb-world-group" style="margin:0 8px 6px 0">
            <div style="padding:7px 14px 5px 18px; font-size:1em; opacity:0.90;">Worldbuilding</div>
            <div id="sb-world-list" style="padding-left:28px; font-size:0.97em; color:#ccc;">No world elements yet.</div>
          </div>
          <div style="flex:1"></div>
          <div style="padding:14px 12px 18px 12px; border-top:1px solid #333;">
            <button id="open-settings-btn" style="width:100%; padding:8px; border-radius:6px; border:none; background:#463fff; color:#fafafe; font-size:1em; font-weight:bold; cursor:pointer;">Settings</button>
          </div>
        `;
      }

      function renderMainPanel() {
        const mp = document.querySelector(".main-panel");
        const docs = window.PSW.documents || [];
        const uiState = window.PSW.uiState || {};
        let activeDocId = uiState.activeDocId;
        if (docs.length > 0 && (!activeDocId || !docs.some(d => d.id === activeDocId))) {
          activeDocId = docs[0].id;
        }
        let tabHtmlArr = [];
        for (let i = 0; i < docs.length; ++i) {
          const d = docs[i];
          const safeTitle = d.title.replace(/</g, "<").replace(/"/g, """);
          tabHtmlArr.push(
            '<button data-docid="' + d.id + '" class="doc-tab" style="'
            + 'margin-right:8px; padding:7px 15px;'
            + 'background:' + (d.id === activeDocId ? '#463fff' : '#ececff') + ';'
            + 'color:' + (d.id === activeDocId ? '#fafafe' : '#23232b') + ';'
            + 'border:none; border-radius:6px 6px 0 0; font-weight:500; cursor:pointer;"'
            + ' title="Select chapter">'
            + safeTitle + '</button>'
          );
        }
        tabHtmlArr.push(
          '<button id="add-chapter-btn" style="margin-left:auto; padding:7px 16px; background:#e8e8ff; color:#23232b; border:none; border-radius:7px; font-weight:600; font-size:1em; cursor:pointer;">+ New Chapter</button>'
        );
        let tabs = '<div style="display:flex; align-items:center; border-bottom:1px solid #eee; padding:8px 12px 0 12px; background:#f7f7fa;">'
          + tabHtmlArr.join("") + '</div>';
        let body = "";
        const currDoc = docs.find(d => d.id === activeDocId);
        if (currDoc) {
          const safeDocTitle = currDoc.title.replace(/</g, "<").replace(/"/g, """);
          body =
            '<div style="padding:32px;">'
            + '<div style="display:flex; align-items:center; margin-bottom:20px;">'
            + '<input id="doc-title" type="text" value="' + safeDocTitle + '" style="font-size:1.28em; font-weight:600; border:none; background:inherit; border-bottom:1.5px solid #ccc; padding:7px 10px 3px 0; width:60%;">'
            + '<button id="delete-doc-btn" title="Delete chapter" style="margin-left:auto; background:#ee2442; color:#fafafe; border:none; border-radius:5px; font-weight:bold; padding:8px 14px; font-size:1em; cursor:pointer;">Delete</button>'
            + '</div>'
            + '<div style="font-size:1em; color:#888;">(Editor coming next)</div>'
            + '</div>';
        } else {
          body =
            '<div style="padding:38px 0 0 0; display:flex;flex-direction:column;align-items:center; height:100%;">'
            + '<div style="font-size:1.2em; font-weight:600; color:#463fff; letter-spacing:1px;">PseudoWrite</div>'
            + '<div style="padding:24px 0 0 0; color:#333; opacity:0.7;">Select or add a document/chapter to begin.</div>'
            + '</div>';
        }
        mp.innerHTML = tabs + body;

        mp.querySelector("#add-chapter-btn").onclick = function() {
          let n = 1;
          let name = "Chapter 1";
          while (docs.some(d => d.title === name)) { n++; name = "Chapter " + n; }
          const id = "doc_" + Date.now() + "_" + Math.floor(Math.random()*10000);
          docs.push({ id, title: name, content: "" });
          window.PSW.documents = docs;
          window.PSW.uiState.activeDocId = id;
          renderMainPanel();
        };
        mp.querySelectorAll(".doc-tab").forEach(function(btn) {
          btn.onclick = function() {
            window.PSW.uiState.activeDocId = btn.getAttribute("data-docid");
            renderMainPanel();
          };
        });
        if (currDoc) {
          mp.querySelector("#doc-title").addEventListener("change", function() {
            const idx = docs.findIndex(d => d.id === currDoc.id);
            if (idx >= 0) {
              docs[idx].title = this.value;
              window.PSW.documents = docs;
              renderMainPanel();
            }
          });
          mp.querySelector("#delete-doc-btn").onclick = function() {
            const idx = docs.findIndex(d => d.id === currDoc.id);
            if (idx >= 0) {
              docs.splice(idx, 1);
              window.PSW.documents = docs;
              window.PSW.uiState.activeDocId = docs.length ? docs[0].id : null;
              renderMainPanel();
            }
          };
        }
      }

      renderSidebar();
      renderMainPanel();
    });
  </script>
</body>
</html>
