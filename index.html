<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=1024, initial-scale=1.0">
  <title>PseudoWrite – AI Storywriting Assistant</title>
  <style>
    /* Minimal CSS Reset */
    html, body, div, span, applet, object, iframe,
    h1, h2, h3, h4, h5, h6, p, blockquote, pre,
    a, abbr, acronym, address, big, cite, code,
    del, dfn, em, img, ins, kbd, q, s, samp,
    small, strike, strong, sub, sup, tt, var,
    b, u, i, center,
    dl, dt, dd, ol, ul, li,
    fieldset, form, label, legend,
    table, caption, tbody, tfoot, thead, tr, th, td,
    article, aside, canvas, details, embed,
    figure, figcaption, footer, header, hgroup,
    menu, nav, output, ruby, section, summary,
    time, mark, audio, video {
      margin: 0; padding: 0; border: 0; font-size: 100%; font: inherit; vertical-align: baseline;
    }
    body {
      font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
      background: #f7f7fa;
      color: #23232b;
      min-width: 1024px;
      line-height: 1.5;
    }
    /* Layout grid */
    #app-root {
      display: flex;
      width: 100vw;
      height: 100vh;
      box-sizing: border-box;
      overflow: hidden;
    }
    .sidebar {
      width: 260px;
      background: #23232b;
      color: #fafafe;
      display: flex;
      flex-direction: column;
      border-right: 1px solid #ddd;
      z-index: 1;
    }
    .main-panel {
      flex: 1;
      background: #fff;
      position: relative;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    #modal-root {
      position: fixed;
      z-index: 1000;
      top: 0; left: 0; width: 100vw; height: 100vh;
      pointer-events: none;
    }
    #toast-root {
      position: fixed;
      z-index: 1100;
      bottom: 32px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      pointer-events: none;
    }
    /* Hidden by default */
    [data-hidden="true"] { display: none !important; }
  </style>
</head>
<body>
  <div id="app-root">
    <aside class="sidebar">
      <!-- Sidebar content dynamically generated -->
    </aside>
    <main class="main-panel">
      <!-- Main workspace dynamically generated -->
    </main>
  </div>
  <div id="modal-root"></div>
  <div id="toast-root"></div>
  <script>
    // ===== PseudoWrite State Schema and Management =====
    // Define domains:
    // - storyBible: { characters: Character[], characterOrder: string[], worldbuilding: WorldElement[], worldOrder: string[] }
    // - documents: { id: string, title: string, content: string }[]
    // - uiState: { activePanel: string, openModal: null|string, sidebarState: {...}, ... }
    // - settings: { apiKey: string, apiEndpoint: string }

    // ===== Type Commentary (for reference) =====
    // Character: { id: string, name: string, role: string, appearance: string, personality: string, goals: string, flaws: string, description: string }
    // WorldElement: { id: string, name: string, type: string, description: string, significance: string }
    // Document: { id: string, title: string, content: string }

    window.PSW = {
      storyBible: {
        characters: [],
        characterOrder: [],
        worldbuilding: [],
        worldOrder: []
      },
      documents: [],
      uiState: {
        activePanel: "editor",
        openModal: null,
        sidebarState: {},
      },
      settings: {
        apiKey: "",
        apiEndpoint: "https://text.pollinations.ai/openai/v1/chat/completions"
      }
    };

    // Utility: safe JSON parse
    function safeParse(json) {
      try { return JSON.parse(json); } catch { return null; }
    }

    function loadStoryBible() {
      const raw = localStorage.getItem("psw_storyBible");
      if (raw) {
        const val = safeParse(raw);
        if (val && typeof val === "object"
          && Array.isArray(val.characters)
          && Array.isArray(val.characterOrder)
          && Array.isArray(val.worldbuilding)
          && Array.isArray(val.worldOrder)) return val;
      }
      return structuredClone({
        characters: [],
        characterOrder: [],
        worldbuilding: [],
        worldOrder: []
      });
    }
    function saveStoryBible(sb) {
      try {
        localStorage.setItem("psw_storyBible", JSON.stringify(sb));
        window.PSW.storyBible = sb;
      } catch (e) { console.warn("Failed to save storyBible:", e); }
    }
    function loadDocuments() {
      const raw = localStorage.getItem("psw_documents");
      if (raw) {
        const val = safeParse(raw);
        if (Array.isArray(val)) return val;
      }
      return [];
    }
    function saveDocuments(docs) {
      try {
        localStorage.setItem("psw_documents", JSON.stringify(docs));
        window.PSW.documents = docs;
      } catch (e) { console.warn("Failed to save documents:", e); }
    }
    function loadSettings() {
      const raw = localStorage.getItem("psw_settings");
      if (raw) {
        const val = safeParse(raw);
        if (val && typeof val === "object" && typeof val.apiEndpoint === "string") {
            return Object.assign({}, { apiKey: "", apiEndpoint: "https://text.pollinations.ai/openai/v1/chat/completions" }, val);
        }
      }
      return {
        apiKey: "",
        apiEndpoint: "https://text.pollinations.ai/openai/v1/chat/completions"
      };
    }
    function saveSettings(settings) {
      try {
        localStorage.setItem("psw_settings", JSON.stringify(settings));
        window.PSW.settings = settings;
      } catch (e) { console.warn("Failed to save settings:", e); }
    }
    function loadUIState() {
      const raw = localStorage.getItem("psw_uiState");
      if (raw) {
        const val = safeParse(raw);
        if (val && typeof val === "object") return val;
      }
      return {
        activePanel: "editor",
        openModal: null,
        sidebarState: {},
      };
    }
    function saveUIState(state) {
      try {
        localStorage.setItem("psw_uiState", JSON.stringify(state));
        window.PSW.uiState = state;
      } catch (e) { console.warn("Failed to save uiState:", e); }
    }
    function validateCharacter(obj) {
      return obj &&
        typeof obj === "object" &&
        typeof obj.id === "string" &&
        typeof obj.name === "string" &&
        typeof obj.role === "string" &&
        typeof obj.appearance === "string" &&
        typeof obj.personality === "string" &&
        typeof obj.goals === "string" &&
        typeof obj.flaws === "string" &&
        typeof obj.description === "string";
    }
    function validateWorldElement(obj) {
      return obj &&
        typeof obj === "object" &&
        typeof obj.id === "string" &&
        typeof obj.name === "string" &&
        typeof obj.type === "string" &&
        typeof obj.description === "string" &&
        typeof obj.significance === "string";
    }

    // Minimal Inline Test Harness
    function runTests() {
      let passed = 0, failed = 0;
      function assert(name, cond) {
        if (cond)
          { passed++; console.log('%cPASS%c ' + name, 'color:green', 'color:inherit'); }
        else
          { failed++; console.error('%cFAIL%c ' + name, 'color:red', 'color:inherit'); }
      }
      // Character validation tests
      const validChar = { id: "1", name: "Jane", role: "Protagonist", appearance: "Tall", personality: "Calm", goals: "Find peace", flaws: "Impatient", description: "Detective" };
      const invalidChar = { id: 2, name: 123, role: "", appearance: true };
      assert("validateCharacter: valid", validateCharacter(validChar));
      assert("validateCharacter: invalid", !validateCharacter(invalidChar));
      // World element validation tests
      const validWorld = { id: "w1", name: "Kingdom", type: "Location", description: "A land...", significance: "Main setting" };
      const invalidWorld = { id: null, name: false, type: 7, description: undefined };
      assert("validateWorldElement: valid", validateWorldElement(validWorld));
      assert("validateWorldElement: invalid", !validateWorldElement(invalidWorld));
      // Persistence roundtrip (documents)
      const docs = [{ id: "a", title: "Chap1", content: "x" }];
      saveDocuments(docs);
      let loadedDocs = loadDocuments();
      assert("save/load documents", Array.isArray(loadedDocs) && loadedDocs.length === 1 && loadedDocs[0].id === "a");
      // Settings default/override
      saveSettings({ apiKey: "abc", apiEndpoint: "test" });
      let loadedSettings = loadSettings();
      assert("save/load settings", loadedSettings.apiKey === "abc" && loadedSettings.apiEndpoint === "test");
      // UI state roundtrip
      saveUIState({ activePanel: "storyBible", openModal: "modalId", sidebarState: { foo: 1 } });
      let loadedUI = loadUIState();
      assert("save/load uiState", loadedUI.activePanel === "storyBible" && loadedUI.openModal === "modalId" && loadedUI.sidebarState.foo === 1);
      // Output
      const summary = `Test suite finished: ${passed} passed, ${failed} failed`;
      if (failed === 0)
        { console.log('%c' + summary, 'color:green'); }
      else
        { console.error('%c' + summary, 'color:red'); }
    }

    document.addEventListener('DOMContentLoaded', function() {
      console.log('PseudoWrite: App initialized');
      runTests();

      // === Dynamic Sidebar & Main Panel Shell ===
      function renderSidebar() {
        const sidebar = document.querySelector(".sidebar");
        sidebar.innerHTML = `
          <div style="padding: 20px 16px 8px 16px; font-weight:bold; letter-spacing:1px; font-size:1.15em;">Story Bible</div>
          <div id="sb-characters-group" style="margin: 0 0 6px 0">
            <div style="padding:7px 14px 5px 18px; font-size:1em; opacity:0.90;">Characters</div>
            <div id="sb-characters-list" style="padding-left:28px; font-size:0.97em; color:#ccc;">No characters yet.</div>
          </div>
          <div id="sb-world-group" style="margin: 0 8px 6px 0">
            <div style="padding:7px 14px 5px 18px; font-size:1em; opacity:0.90;">Worldbuilding</div>
            <div id="sb-world-list" style="padding-left:28px; font-size:0.97em; color:#ccc;">No world elements yet.</div>
          </div>
          <div style="flex:1"></div>
          <div style="padding: 14px 12px 18px 12px; border-top: 1px solid #333;">
            <button id="open-settings-btn" style="width:100%; padding:8px; border-radius:6px; border:none; background:#463fff; color:#fafafe; font-size:1em; font-weight:bold; cursor:pointer;">Settings</button>
          </div>
        `;
      }

      function renderMainPanel() {
        const mp = document.querySelector(".main-panel");
        // Load latest documents and state on each render
        const docs = loadDocuments();
        const uiState = loadUIState();
        let activeDocId = uiState.activeDocId;
        if (docs.length > 0 && (!activeDocId || !docs.some(d => d.id === activeDocId))) {
          activeDocId = docs[0].id;
          saveUIState({ ...uiState, activeDocId });
        }
        // Tab bar for documents
        let tabHtmlArr = [];
        for (let i = 0; i < docs.length; ++i) {
          const d = docs[i];
          // Escape < and " for HTML safety
          const safeTitle = d.title.replace(/</g, "<").replace(/"/g, """);
          tabHtmlArr.push(
            '<button data-docid="' + d.id + '" class="doc-tab" style="'
            + 'margin-right:8px; padding:7px 15px;'
            + 'background:' + (d.id === activeDocId ? '#463fff' : '#ececff') + ';'
            + 'color:' + (d.id === activeDocId ? '#fafafe' : '#23232b') + ';'
            + 'border:none; border-radius:6px 6px 0 0; font-weight:500; cursor:pointer;"'
            + ' title="Select chapter">'
            + safeTitle + '</button>'
          );
        }
        tabHtmlArr.push(
          '<button id="add-chapter-btn" style="margin-left:auto; padding:7px 16px; background:#e8e8ff; color:#23232b; border:none; border-radius:7px; font-weight:600; font-size:1em; cursor:pointer;">+ New Chapter</button>'
        );
        let tabs = '<div style="display:flex; align-items:center; border-bottom:1px solid #eee; padding:8px 12px 0 12px; background:#f7f7fa;">'
          + tabHtmlArr.join("") + '</div>';
        // Main body: show selected doc or hello
        let body = "";
        const currDoc = docs.find(d => d.id === activeDocId);
        if (currDoc) {
          const safeDocTitle = currDoc.title.replace(/</g, "<").replace(/"/g, """);
          body =
            '<div style="padding:32px;">'
            + '<div style="display:flex; align-items:center; margin-bottom:20px;">'
            + '<input id="doc-title" type="text" value="' + safeDocTitle + '" style="font-size:1.28em; font-weight:600; border:none; background:inherit; border-bottom:1.5px solid #ccc; padding:7px 10px 3px 0; width:60%;">'
            + '<button id="delete-doc-btn" title="Delete chapter" style="margin-left:auto; background:#ee2442; color:#fafafe; border:none; border-radius:5px; font-weight:bold; padding:8px 14px; font-size:1em; cursor:pointer;">Delete</button>'
            + '</div>'
            + '<div style="font-size:1em; color:#888;">(Editor coming next)</div>'
            + '</div>';
        } else {
          body =
            '<div style="padding: 38px 0 0 0; display:flex;flex-direction:column;align-items:center; height:100%;">'
            + '<div style="font-size:1.2em; font-weight:600; color:#463fff; letter-spacing:1px;">PseudoWrite</div>'
            + '<div style="padding:24px 0 0 0; color:#333; opacity:0.7;">Select or add a document/chapter to begin.</div>'
            + '</div>';
        }
        mp.innerHTML = tabs + body;

        // Event: Add new chapter
        mp.querySelector("#add-chapter-btn").onclick = function() {
          const docs = loadDocuments();
          let n = 1;
          let name = "Chapter 1";
          while (docs.some(d => d.title === name)) { n++; name = "Chapter " + n; }
          const id = "doc_" + Date.now() + "_" + Math.floor(Math.random()*10000);
          docs.push({ id, title: name, content: "" });
          saveDocuments(docs);
          saveUIState({ ...loadUIState(), activeDocId: id });
          renderMainPanel();
        };

        // Event: Switch chapters
        mp.querySelectorAll(".doc-tab").forEach(function(btn) {
          btn.onclick = function() {
            const id = btn.getAttribute("data-docid");
            saveUIState({ ...loadUIState(), activeDocId: id });
            renderMainPanel();
          };
        });

        // Event: Title edit
        if (currDoc) {
          mp.querySelector("#doc-title").addEventListener("change", function() {
            const docs = loadDocuments();
            const idx = docs.findIndex(d => d.id === currDoc.id);
            if (idx >= 0) {
              docs[idx].title = this.value;
              saveDocuments(docs);
              renderMainPanel();
            }
          });
          mp.querySelector("#delete-doc-btn").onclick = function() {
            let docs = loadDocuments();
            const idx = docs.findIndex(d => d.id === currDoc.id);
            if (idx >= 0) {
              docs.splice(idx, 1);
              saveDocuments(docs);
              // Update selected doc
              const remaining = docs.length ? docs[0].id : null;
              saveUIState({ ...loadUIState(), activeDocId: remaining });
              renderMainPanel();
            }
          };
        }
      }

      renderSidebar();
      renderMainPanel();
    });
  </script>
</body>
</html>
