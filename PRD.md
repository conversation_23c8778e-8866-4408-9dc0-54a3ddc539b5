Of course. Here is the PRD updated to integrate the new features by describing the final state of each component, rather than breaking it down into phases.

---

### **Overview**

PseudoWrite is a single-file, browser-based AI writing assistant designed for creative writers. It solves the problems of writer's block, plot development, and prose refinement by acting as an intelligent co-writer. Its target users are fiction authors, hobbyist writers, and anyone looking for a private, powerful tool to augment their creative process without subscriptions or cloud-based data storage.

The core value of PseudoWrite is its "Story Bible" concept. By providing the AI with a deep, **structured context** about the story's characters, world, and plot, the AI's assistance becomes highly relevant, consistent, and personalized to the user's specific narrative. Because it's a single-file application that runs locally and stores data in the browser, it offers complete privacy and requires no setup, making it an accessible and secure creative partner.

### **Core Features**

1.  **Structured Story Bible:** The central, database-like knowledge base for the AI.
    *   **What it does:** Allows users to input, manage, and categorize all foundational elements of their story. This includes a free-form Braindump, Genre, Style, Synopsis, and a structured Outline.
    *   **Character and Worldbuilding Management:**
        *   **Structured Data:** `Characters` are stored with detailed fields (`name`, `role`, `appearance`, `personality`, `goals`, `flaws`, `description`). `Worldbuilding` elements are stored with (`name`, `type`, `description`, `significance`).
        *   **Categorized Navigation:** The sidebar automatically groups Characters by their `role` (Protagonist, Antagonist, etc.) and Worldbuilding elements by their `type` (Location, Faction, etc.) into collapsible accordion sections, making large lists easy to navigate.
        *   **Detail Modals:** Clicking on any character or worldbuilding item opens a dedicated modal window to view and edit all of its structured fields in one place.
    *   **Why it's important:** This structured context transforms the AI from a generic text generator into a knowledgeable co-author. The categorization makes the Story Bible easy to navigate, and the detailed fields ensure the AI has precise information to draw upon, drastically improving the quality and consistency of all generated content.

2.  **AI-Powered Generation & Refinement:** Tools for populating and enhancing the Story Bible.
    *   **What it does:** Provides AI assistance to create and structure story elements.
        *   **`Generate Synopsis` & `Generate Outline`:** Standard AI generation for high-level story structure.
        *   **`Generate Character/Worldbuilding Element`:** Takes a simple user prompt and uses the AI to generate a **complete, structured JSON object** for a new character or worldbuilding element, populating all its detailed fields at once.
        *   **`Refine with AI` (within Modals):** A powerful feature within the detail modals. The AI analyzes an existing description of a character or element and automatically extracts or infers information to populate all other structured fields (e.g., it infers `flaws` and `goals` from a character's bio).
    *   **Why it's important:** This significantly speeds up the worldbuilding and character creation process. It allows users to start with a simple idea or an unstructured block of text and quickly transform it into a rich, detailed, and organized entry.

3.  **AI-Powered Writing Tools:** The core interactive features within the main text editor.
    *   **What it does:** Provides on-demand assistance for drafting, rewriting, and enhancing prose. This includes `Write` (to continue the story), `Rewrite` (to get alternative phrasing), and `Describe` (to add sensory detail).
    *   **Why it's important:** These tools directly address common writing challenges, helping users overcome creative hurdles, improve their writing quality, and maintain momentum. They draw context from the deeply structured Story Bible, making their suggestions more accurate.
    *   **How it works:** The user selects text or places their cursor and clicks a button. The application sends the local text context and the full structured Story Bible to the AI with a specialized prompt. The AI's response is then displayed for the user to accept or reject.

4.  **Local-First & Private:** The foundational architecture of the application.
    *   **What it does:** The entire application runs within a single HTML file. All data is stored exclusively in the browser's `localStorage`. No data is ever sent to a server, except for the anonymous, on-demand requests to the AI endpoint.
    *   **Why it's important:** This guarantees absolute user privacy and data ownership, a major concern for writers working on original material. It also makes the application incredibly portable and easy to use—no installation or login required.

### **User Experience**

*   **User Personas:**
    *   **The Hobbyist Author:** Writes for pleasure and wants a tool to help them stay motivated and explore ideas without the pressure or cost of professional software.
    *   **The NaNoWriMo Participant:** Needs to write a large volume of text quickly. Uses the `Write` and `Brainstorm` features to maintain a high word count.
    *   **The Worldbuilder:** Spends significant time developing fictional worlds. Uses the structured `Worldbuilding` and `Characters` sections with their AI generation and refinement tools to build a deep, navigable, and consistent universe before starting to write prose.

*   **Key User Flows:**
    1.  **Deep Character Creation Flow:**
        *   User clicks "Generate Character" and provides a prompt: "a cynical detective who loves cats."
        *   AI generates a new character with all structured fields filled. The character automatically appears in the sidebar under the "Protagonist" category.
        *   User clicks the new character, and a detail modal opens. User edits the AI-generated `flaws` to be more specific and clicks "Save Changes."
    2.  **Refinement & Structuring Flow:**
        *   User manually adds a new Worldbuilding element, giving it a name and pasting a long, unstructured paragraph into the `description` field.
        *   User clicks the item to open its detail modal.
        *   User clicks **"Refine Details with AI."** The AI analyzes the description and automatically populates the `type` and `significance` fields in the modal.
        *   User reviews the AI's work and clicks "Save Changes." The item is now automatically categorized in the sidebar.

*   **UI/UX Considerations:**
    *   The UI must be clean and intuitive. The sidebar's collapsible categories must make navigating a large number of characters/worldbuilding elements manageable.
    *   **Modal windows** for detail editing must be well-designed, with clearly labeled fields and intuitive save/delete/close actions.
    *   AI interactions should provide clear feedback, such as a "loading" state on buttons, to manage user expectations during API calls.

---

### **Technical Architecture**

*   **System Components:**
    *   **`index.html`:** A single static file containing all HTML (including modal structures), CSS, and JavaScript.
    *   **Browser:** The runtime environment, utilizing `localStorage` and the `fetch` API.
    *   **Pollinations API:** The external AI service endpoint.

*   **Data Models:**
    *   A single JavaScript object, `appState`, will be held in memory and serialized to `localStorage`.
    *   The `storyBible` object within `appState` will contain `characters` and `worldbuilding` arrays. The objects within these arrays will adhere to the following structured formats:
    *   **Character Object Structure:** `{ id, name, role, appearance, personality, goals, flaws, description }`
    *   **Worldbuilding Element Object Structure:** `{ id, name, type, description, significance }`

*   **APIs and Integrations:**
    *   **Primary Integration:** `https://text.pollinations.ai/openai/v1/chat/completions`.
    *   **AI Prompts:** Prompts for character/worldbuilding generation and refinement will be engineered to explicitly request **structured JSON output**, which will then be parsed client-side. A `parseAIJson` helper function will be used to robustly handle the AI's response.

*   **Infrastructure Requirements:**
    *   None. User needs a modern web browser.

### **Development Roadmap**

*   **MVP Requirements:**
    1.  A functional single-page application with a static HTML/CSS layout.
    2.  `localStorage` persistence for a single project.
    3.  A fully interactive (non-AI) Story Bible with the structured, categorized lists and detail modals for Characters and Worldbuilding.
    4.  The core AI features: `Write` (Auto mode), `Rewrite`, `Generate Character` (as structured JSON), and `Refine with AI` within the modals.
*   **Future Enhancements:**
    1.  Full multi-project management (switching, deleting projects).
    2.  Implementation of all remaining AI tools (`Describe`, `Brainstorm`, all "Plugin" features).
    3.  An "Export/Import Project to JSON" feature to manage `localStorage` space and back up work.
    4.  UI refinements, including better loading states and error notifications.

### **Logical Dependency Chain**

1.  **Data Schema:** The structured data models for Characters and Worldbuilding are the absolute foundation.
2.  **Static UI & State:** The HTML/CSS layout, including modals and categorized lists, and the `localStorage` logic to handle the new state structure.
3.  **Modal Interactivity:** The ability to open, edit, and save data in the detail modals.
4.  **Structured AI Logic:** The `parseAIJson` helper and the updated generation/refinement prompts that request JSON output.
5.  **Core Writing Tools:** The main editor tools (`Write`, etc.) build upon the rich context provided by the completed structured Story Bible.

### **Risks and Mitigations**

*   **Technical Challenge: AI Generating Invalid JSON.**
    *   **Risk:** The LLM may not always return perfectly formatted JSON, which would cause client-side parsing errors.
    *   **Mitigation:** Implement a robust `parseAIJson` helper function that can handle common formatting issues (like markdown code blocks). Wrap all `JSON.parse()` calls in `try...catch` blocks. On failure, display a user-friendly alert that includes a snippet of the raw AI output.
*   **Technical Challenge: Prompt Engineering Complexity.**
    *   **Risk:** Crafting prompts that consistently produce high-quality, structured JSON for both generation and refinement is difficult and requires iteration.
    *   **Mitigation:** Start with very explicit, detailed prompts. Develop one feature at a time (e.g., character generation) and test it thoroughly, refining the prompt until the output is reliable before moving to the next feature.
*   **UI Complexity:**
    *   **Risk:** The addition of modals and nested lists adds complexity to the UI state management and rendering logic.
    *   **Mitigation:** Keep the UI logic clean by creating dedicated functions for opening/closing/populating each modal. Use event delegation for list item clicks to simplify event handling.

### **Appendix**

*   **Technical Specification: `parseAIJson` function**
    *   Must accept a raw string from the AI as input.
    *   Must be able to strip leading/trailing whitespace and handle JSON wrapped in markdown code fences (` ```json ... ``` `).
    *   Should return a parsed JavaScript object.
*   **Research Finding: `localStorage` Limits**
    *   `localStorage` typically has a limit of around 5MB per domain. The new, more detailed structured data will consume this space faster than simple text lists. This reinforces the need for a future "Export/Import Project" feature.